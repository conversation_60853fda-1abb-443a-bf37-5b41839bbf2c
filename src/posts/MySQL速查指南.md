### 基本 MySQL 配置

设置 root 用户密码（第一次设置或重置）：

```bash
ALTER USER 'root'@'localhost' IDENTIFIED BY 'yourpassword';
```

登录 MySQL：

```bash
mysql -u root -p
```

修改密码：

```bash
ALTER USER 'root'@'localhost' IDENTIFIED BY 'yourpassword';
```

查看现有数据库：

```bash
SHOW DATABASES;
```

查看表结构：

```bash
DESC users;

# 详细
SHOW COLUMNS FROM users;
```

查看表数据：

```bash
SELECT * FROM users;

# 限制条数
SELECT * FROM users LIMIT 100;
```

查找并更新手机号为某值的用户：

```bash
UPDATE users
SET is_admin = 1, role = 'admin'
WHERE mobile = '13490342903';
```

删除符合条件的记录：

```bash
DELETE FROM users WHERE mobile = '13490342903';
```

清空操作历史记录：

```bash
sudo bash -c '> /root/.mysql_history'
```

查看 .mysql_history 的内容：

```bash
cat ~/.mysql_history
```
