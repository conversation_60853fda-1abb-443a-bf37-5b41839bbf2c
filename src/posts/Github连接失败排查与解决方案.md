# GitHub 连接失败排查与解决方案

记录 GitHub 克隆/请求失败时的三种解决方式，适用于 `git clone`、`git pull`、`git push`、子模块同步、raw.githubusercontent.com 加载失败等问题。

---

## 1. 改用 SSH clone

HTTPS 克隆经常超时，可以用 SSH 地址代替：

```bash
<NAME_EMAIL>:owner/repo.git
```

需先配置 SSH key，GitHub 上添加公钥。

优点：一般比 HTTPS 稳定。
缺点：私有仓库需要配置权限。

---

## 2. 使用国内反代镜像加速

临时解决方案，适合 clone 公有仓库：

```bash
git clone https://github.com.cnpmjs.org/owner/repo.git
# 或
git clone https://ghproxy.com/https://github.com/owner/repo.git
# 或
git clone https://hub.fgit.ml/owner/repo.git
```

注意：稳定性不保证，推荐用于一次性操作。

---

## 3. 修复 DNS 污染或连接异常（hosts/DNS）

有时 DNS 被污染或 github.com IP 被屏蔽，可尝试：

1. ping GitHub 获得实际 IP：

```bash
ping github.com
```

2. 修改 `/etc/hosts` 添加 GitHub 的解析：

```bash
sudo vim /etc/hosts

************ github.com
```
