# K8s Nginx 504 错误排查总结

## 问题描述

在 K8s 集群中，某个模块（sg-org-api）突然出现 nginx 504 Gateway Timeout 错误，而其他服务都正常运行。

**错误表现：**

- 接口：`GET /organization/login`
- 状态码：504 Gateway Timeout
- 现象：其他服务正常，只有 org 相关接口报错

## 核心要点

> **关键总结：nginx 报错应该直接查 nginx 日志！**

## 排查过程

### 1. 初始症状分析

```bash
# 查看应用状态
kubectl get pods -n prod -l app=sg-org-api
kubectl describe pod sg-org-api-xxx -n prod
```

**初步发现：**

- Pod 状态正常（Running）
- 应用启动日志正常
- 但外部请求没有到达应用层

### 2. 网络连通性测试

```bash
# 测试服务健康检查
kubectl exec -it other-pod -n prod -- wget http://sg-org-service:19527/health

# 结果：健康检查正常，响应时间只有 14ms
```

**排除：** 应用层问题

### 3. 🚨 关键突破 - 查看 nginx controller 日志

```bash
# 查找 nginx ingress controller
kubectl get pods --all-namespaces | grep -E "(ingress|nginx|controller)"

# 找到：kubesphere-router-kubesphere-system-xxx
kubectl logs kubesphere-router-kubesphere-system-xxx -n kubesphere-controls-system --tail=50
```

**关键发现：**

```
[emerg] "proxy_redirect" directive is duplicate in /tmp/nginx/nginx-cfg3159276297:724
nginx: [emerg] "proxy_redirect" directive is duplicate
nginx: configuration file /tmp/nginx/nginx-cfg3159276297 test failed
Error reloading NGINX
```

**这是整个排查的转折点！** nginx 配置重载失败。

### 4. 定位配置问题

```bash
# 检查 ingress 配置
kubectl get ingress api-v1-sengox-cn -n prod -o yaml | grep -A 50 annotations:
```

**发现配置格式错误：**

```yaml
nginx.ingress.kubernetes.io/configuration-snippet: |
  add_header 'Access-Control-Allow-Headers' 'xxx' always;  proxy_redirect off; proxy_set_header Host $host;
```

问题：多个 nginx 指令挤在一行，导致配置冲突。

## 解决方案

### 1. 修复配置格式

在 KubeSphere 界面中编辑 Ingress，修复 `configuration-snippet`：

**错误格式：**

```yaml
add_header 'xxx' always;  proxy_redirect off; proxy_set_header Host $host;
```

**正确格式：**

```yaml
nginx.ingress.kubernetes.io/configuration-snippet: >
  set $corsHost "";
  if ($http_origin ~* "^https?:\/\/([a-z0-9-]+\.)*sengox\.cn$") {
    set $corsHost $http_origin;
  }
  # 其他正确的 nginx 指令...
```

### 2. 保留超时配置

```yaml
nginx.ingress.kubernetes.io/proxy-connect-timeout: "300"
nginx.ingress.kubernetes.io/proxy-read-timeout: "300"
nginx.ingress.kubernetes.io/proxy-send-timeout: "300"
```

### 3. 验证修复

```bash
# 保存配置后，nginx 会自动重新加载
# 测试接口
curl -v https://api-v1.sengox.cn/organization/login
```

## 排查思路总结

### ✅ 正确的排查顺序

1. **查看症状** → 504 错误
2. **分层排查** → 应用层 → 服务层 → 网关层
3. **🚨 查看 nginx 日志** → 发现配置重载失败（关键！）
4. **检查配置** → ingress 注解格式错误
5. **修复配置** → 问题解决

### ❌ 容易犯的错误

- 只关注应用日志，忽略 nginx controller 日志
- 假设配置已生效，不验证配置重载状态
- 没有分层排查，直接怀疑应用问题

## 经验总结

### 关键诊断技巧

1. **nginx 报错 → 直接查 nginx 日志**
2. **分层思维** - 网络请求的每一层都可能有问题
3. **配置验证** - 不要假设配置已生效，要验证重载状态
4. **症状结合** - "日志没反应" + "504 错误" = nginx 层问题

### nginx 504 标准排查流程

```bash
# 1. 查看 nginx controller 日志（最重要！）
kubectl logs nginx-controller-pod -n namespace --tail=50

# 2. 测试应用层连通性
kubectl exec -it pod -- curl service:port/health

# 3. 检查 ingress 配置格式
kubectl get ingress ingress-name -o yaml

# 4. 验证服务端点状态
kubectl get endpoints service-name
```

### 常见 nginx 配置错误

- `configuration-snippet` 格式错乱
- 多个指令挤在一行
- 与默认配置冲突
- 语法错误导致重载失败

## 预防措施

1. **配置规范化** - 使用正确的 YAML 格式
2. **测试验证** - 配置修改后检查 nginx 重载状态
3. **监控告警** - 监控 nginx controller 配置重载失败事件
4. **文档记录** - 记录 ingress 配置的修改历史

---

**核心要点再次强调：遇到 nginx 相关问题，第一时间查看 nginx controller 日志！**
