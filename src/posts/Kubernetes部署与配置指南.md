# Kubernetes 部署与配置指南

## K8s CORS

```bash
nginx.ingress.kubernetes.io/enable-cors: "true"
nginx.ingress.kubernetes.io/cors-allow-origin: "https://a.xxx.cn"
nginx.ingress.kubernetes.io/cors-allow-methods: "GET, PUT, POST, DELETE, PATCH, OPTIONS"
nginx.ingress.kubernetes.io/cors-allow-headers: "Authorization, Content-Type, Accept, Origin"
nginx.ingress.kubernetes.io/cors-allow-credentials: "true"
nginx.ingress.kubernetes.io/cors-max-age: "3600"
```

支持多 Origin

```bash
# nginx.ingress.kubernetes.io/configuration-snippet: |
#   set $cors_origin "";
#   if ($http_origin ~* "^https://(crm|wms|oms|plm|accounts)\.abc\.com$") {
#     set $cors_origin $http_origin;
#   }
#   add_header 'Access-Control-Allow-Origin' "$cors_origin" always;
#   add_header 'Access-Control-Allow-Credentials' 'true' always;

kubesphere.io/creator: admin

nginx.ingress.kubernetes.io/use-forwarded-headers: "true"
nginx.ingress.kubernetes.io/proxy-real-ip-cidr: "0.0.0.0/0"
nginx.ingress.kubernetes.io/configuration-snippet: |
  set $corsHost "";

  if ($http_origin ~* "^https?:\/\/([a-z0-9-]+\.)*sengox\.cn$") {
    set $corsHost $http_origin;
  }

  if ($http_origin = 'null') {
    set $corsHost $http_origin;
  }

  # OPTIONS 请求时先加 header 再 return 204
  if ($request_method = 'OPTIONS') {
    add_header 'Access-Control-Allow-Origin' "$corsHost" always;
    add_header 'Access-Control-Allow-Credentials' 'true' always;
    add_header 'Access-Control-Allow-Methods' 'GET, POST, DELETE, PATCH, OPTIONS' always;
    add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization,X-ClientID,ns,Content-Length,Cookie,ex-clientid' always;
    add_header 'Content-Length' 0;
    add_header 'Content-Type' 'text/plain; charset=UTF-8';
    return 204;
  }

  # 非 OPTIONS 的请求照样加 CORS header
  add_header 'Access-Control-Allow-Origin' "$corsHost" always;
  add_header 'Access-Control-Allow-Credentials' 'true' always;
  add_header 'Access-Control-Allow-Methods' 'GET, POST, DELETE, PATCH, OPTIONS' always;
  add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization,X-ClientID,ns,Content-Length,Cookie,ex-clientid' always;
nginx.ingress.kubernetes.io/rewrite-target: /$2
```

### 参考配置

```bash
nginx.ingress.kubernetes.io/configuration-snippet: |
  set $corsHost "";
  if ($http_origin ~* "^https?:\/\/([a-z0-9-]+\.)*sengox\.cn$") {
    set $corsHost $http_origin;
  }
  if ($http_origin = 'null') {
    set $corsHost $http_origin;
  }
  # OPTIONS 请求时先加 header 再 return 204
  if ($request_method = 'OPTIONS') {
    add_header 'Access-Control-Allow-Origin' "$corsHost" always;
    add_header 'Access-Control-Allow-Credentials' 'true' always;
    add_header 'Access-Control-Allow-Methods' 'GET, POST, DELETE, PATCH, OPTIONS' always;
    add_header 'Access-Control-Allow-Headers' 'DNT,X-Mx-ReqToken,X-token,api-name,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization,app-key,x-client-type,cookie' always;
    add_header 'Content-Length' 0;
    add_header 'Content-Type' 'text/plain; charset=UTF-8';
    return 204;
  }
  # 非 OPTIONS 的请求照样加 CORS header
  add_header 'Access-Control-Allow-Origin' "$corsHost" always;
  add_header 'Access-Control-Allow-Credentials' 'true' always;
  add_header 'Access-Control-Allow-Methods' 'GET, POST, DELETE, PATCH, OPTIONS' always;
  add_header 'Access-Control-Allow-Headers' 'DNT,X-Mx-ReqToken,X-token,api-name,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization,app-key,x-client-type,cookie' always;

  # 传递真实客户端IP
  proxy_set_header Host $host;
  proxy_set_header X-Real-IP $remote_addr;
  proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
  proxy_set_header X-Forwarded-Proto $scheme;
nginx.ingress.kubernetes.io/proxy-connect-timeout: "300"
nginx.ingress.kubernetes.io/proxy-read-timeout: "300"
nginx.ingress.kubernetes.io/proxy-send-timeout: "300"
nginx.ingress.kubernetes.io/rewrite-target: /$2
```

> https://help.aliyun.com/zh/ack/ack-managed-and-ack-dedicated/user-guide/nginx-ingress-cross-domain-configuration-and-common-problems

## Ingress

查看 Ingress 配置

```bash
# 查看当前所有 Ingress
kubectl get ingress -A

# 查看指定 Ingress 的完整配置（YAML）
kubectl get ingress -n dev dev-api-sengox-cn -o yaml
```

```bash
# 修改指定 Ingress 配置
kubectl edit ingress -n dev dev-api-sengox-cn -o yaml

# 备份 Ingress 配置再修改
kubectl get ingress -n dev dev-api-sengox-cn -o yaml > ingress-backup.yaml

# 编辑备份的配置文件
vi ingress-backup.yaml
```

修改完成后，更新：

```bash
kubectl apply -f ingress-backup.yaml
```

## Deployment 操作

1. 查看指定命名空间（例如 dev）下的所有 Deployment

```bash
kubectl get deployments -n dev
```

2. 查看所有命名空间下的 Deployment

```bash
kubectl get deployments -A
```

## KubeConfig

1. 查看有哪些命名空间（确认你要操作的命名空间）

```bash
kubectl get ns
```

2. 查看 KubeConfig 配置路径（确认当前使用的 kubeconfig 文件）

```bash
# kubectl 命令行工具的配置文件，用于客户端连接到 Kubernetes API 服务器
cat ~/.kube/config

# kubelet 组件的配置文件，用于配置节点上的 kubelet 服务如何运行
cat /var/lib/kubelet/config.yaml
```

3. 查看当前使用的 context（集群 + 用户 + 默认命名空间）

```bash
kubectl config get-contexts
kubectl config current-context
```

4. 查看资源配额

```bash
# 查看节点资源分配情况
kubectl describe nodes

# 查看资源配额
kubectl get resourcequotas -A

# 查看限制范围
kubectl get limitranges -A
```

## 节点与 Pod

```bash
# 查看特定节点上的所有Pod
kubectl get pods -A -o wide --field-selector spec.nodeName=<node-name>


# 查看所有命名空间的 Pod 及其所在节点
kubectl get pods -A -o wide


# 查看特定节点的详细信息，包括上面运行的 Pod
kubectl describe node <node-name>

```

### 查看节点上 Pod 的资源使用情况

```bash

# 查看节点上 Pod 的资源请求和限制
kubectl get pods -A -o custom-columns="NAMESPACE:.metadata.namespace,NAME:.metadata.name,NODE:.spec.nodeName,CPU_REQUEST:.spec.containers[*].resources.requests.cpu,MEM_REQUEST:.spec.containers[*].resources.requests.memory,CPU_LIMIT:.spec.containers[*].resources.limits.cpu,MEM_LIMIT:.spec.containers[*].resources.limits.memory" --field-selector spec.nodeName=<node-name>


# 查看所有 Pod 的资源请求和限制
kubectl get pods -A -o custom-columns="NAMESPACE:.metadata.namespace,NAME:.metadata.name,CPU_REQUEST:.spec.containers[*].resources.requests.cpu,MEM_REQUEST:.spec.containers[*].resources.requests.memory,CPU_LIMIT:.spec.containers[*].resources.limits.cpu,MEM_LIMIT:.spec.containers[*].resources.limits.memory"
```

### 查找没有设置资源限制的 Pod

```bash

# 找出没有设置 CPU 请求的 Pod
kubectl get pods -A -o json | jq '.items[] | select((.spec.containers[].resources.requests.cpu == null)) | .metadata.namespace + "/" + .metadata.name'

# 找出没有设置内存请求的 Pod
kubectl get pods -A -o json | jq '.items[] | select((.spec.containers[].resources.requests.memory == null)) | .metadata.namespace + "/" + .metadata.name'
```

### 修改 Pod 资源限制

```bash
# 编辑 Deployment
kubectl edit deployment <deployment-name> -n <namespace>

# 编辑 DaemonSet
kubectl edit daemonset <daemonset-name> -n <namespace>

# 使用 patch 命令修改资源限制
kubectl -n <namespace> patch deployment <deployment-name> --type=json -p='[{"op": "add", "path": "/spec/template/spec/containers/0/resources", "value": {"requests": {"cpu": "100m", "memory": "128Mi"}, "limits": {"cpu": "200m", "memory": "256Mi"}}}]'
```

## 日志

```bash
# 查看所有Pod
kubectl get pods -A -o wide
# 查看指定节点上的Pod
kubectl get pods -A -o wide --field-selector spec.nodeName=node6

# 查看指定Pod日志
kubectl logs <calico-node-ppqrb> -n kube-system

# 查看prod命名空间下的所有Pod
kubectl get pods -n prod

# 实时查看某个服务日志
kubectl logs -n prod sg-user-api-5c4cc6fbcd-wjzwb -f
```

```bash
kubectl exec -it sg-user-api-5c4cc6fbcd-wjzwb -n prod -- \
  wget --header="Content-Type: application/json" \
       --post-data='{"identifier":"13180905568","password":"afZJDyq9ja67xkM6_6"}' \
       -O - http://localhost:19527/user/gateway/login
```

## DNS

1. 确认 DNS Pod 状态

```bash
kubectl get pods -n kube-system -l k8s-app=kube-dns -o wide
kubectl get pods -n kube-system -l k8s-app=coredns -o wide
```

2. 在有问题的节点上测试 DNS 解析

```bash
# 进入有问题节点上的 Pod
kubectl get pods -o wide -A | grep <node-name>
kubectl exec -it <pod-name> -n <namespace> -- /bin/sh

# 在容器里测试
nslookup redis-host
nslookup your-db-host
dig redis-host
dig your-db-host
```

3. 直接在节点上测试 DNS

```bash
# ssh 到有问题的 node
cat /etc/resolv.conf
# 应该指向 ********** 或类似的 coredns ClusterIP

# 用 nslookup/dig 测试
nslookup redis-host **********
dig @********** redis-host
```

4. 看 coredns 日志

```bash
kubectl logs -n kube-system -l k8s-app=kube-dns
kubectl logs -n kube-system -l k8s-app=coredns
```

在 kube-dns Pod 里测试连通性

```bash
kubectl -n kube-system exec -it <kube-dns-pod-name> -- /bin/sh

# 在 Pod 里测试
nslookup www.baidu.com ************
nslookup www.baidu.com ************

# 或直接用 dig
dig @************ www.baidu.com
```
