## 常用命令

# 查看 nginx ingress controller

```bash
# 找不到 pods 时，先查看有哪些
kubectl get pods -n kubesphere-controls-system | grep router
```

# 实时查看包含真实 IP 的访问日志

```bash
kubectl logs -f kubesphere-router-kubesphere-system-6cbcf64666-s25m9 -n kubesphere-controls-system
```

# 只看你的应用相关请求

```bash
kubectl logs -f kubesphere-router-kubesphere-system-6cbcf64666-s25m9 -n kubesphere-controls-system | grep "/app"
```

# 过滤查看组织相关的请求

```bash
kubectl logs -f kubesphere-router-kubesphere-system-6cbcf64666-s25m9 -n kubesphere-controls-system | grep "/organization"
```
