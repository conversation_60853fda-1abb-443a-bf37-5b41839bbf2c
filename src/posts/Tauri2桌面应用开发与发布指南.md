# Tauri2 桌面应用开发与发布指南

## 打包

### 打包指令

1. 打包静态资源

```bash
$ pnpm build:desktop-staging     # 预发布环境
$ pnpm build:desktop-production  # 生产环境
```

2. 打包安装包

- 打包前确认生成静态资源方式，如果 `src-tauri/tauri.conf.json` 文件中，`beforeBuildCommand` 配置了 `npm run build:desktop-staging` 或 `npm run build:desktop-production`，则不需要执行此步骤[1]。
- 如果要更新打包域名，请修改 `src-tauri/tauri.conf.json` 文件中，`frontendDist` 配置。

```bash
$ tauri:build:mac-arm64   # 编译 macOS Apple Silicon 安装包🔥

$ tauri:build:mac-x64     # 编译 macOS Intel 安装包

$ tauri:build:win-x64     # 编译 Windows 安装包🔥
```

```bash
$ tauri:build:mac-debug   # 编译调试 macOS 安装包（默认是 Apple Silicon）

$ tauri:build:win-debug   # 编译调试 Windows 安装包
```

打包完成，根据 `latest.json` 文件，将安装包上传到服务器对应目录。

3. 说明

- 私钥配置：请参考 [Tauri 签名文档](https://v2.tauri.app/plugin/updater/#signing-updates)
- 如果私钥没设置时，则直接回车。

### 打包发布流程

**1. 打包前设置私钥**

设置临时环境变量：

```bash
$ export TAURI_SIGNING_PRIVATE_KEY="$HOME/.tauri/app-name.key"
```

永久设置：变量写入系统配置文件。

```bash
$ vi ~/.zshrc # 添加到文件中，通常放到最后
$ source ~/.zshrc
```

**2. 发布流程：**

每次发布新版本（如 v1.0.1），只需：

- `pnpm build:tauri`（使用私钥打包）
  - 打包完成后，私钥输出在 `src-tauri/target/<debug|release>/bundle/macos/<app-name>.app.tar.gz.sig`.
- 上传新安装包到 `<project>/updates/<client_type>/v1.0.1/`。
- 更新 `<project>/updates/<client_type>/latest.json` 指向 `v1.0.1`。
- **如果打包没有设置私钥密码，则直接回车。**

### Windows

`rustup default` 显示 `stable-aarch64-pc-windows-msvc`，说明你的 Rust 工具链默认是为 ARM64 Windows 环境配置的。

是否能打包 x86_64-pc-windows-msvc？
可以，但需要额外步骤。

默认情况下，`rustup default stable-aarch64-pc-windows-msvc` 只安装了 ARM64 的工具链。要支持 `x86_64-pc-windows-msvc`，你需要手动添加 x86_64 的目标支持。以下是具体步骤：

解决方法
检查当前安装的目标： 运行以下命令，查看已安装的 Rust 目标：

```bash
$ rustup target list --installed
aarch64-pc-windows-msvc

$ rustup target add x86_64-pc-windows-msvc # 安装

$ rustup target list --installed # 确认是否安装成功
aarch64-pc-windows-msvc
x86_64-pc-windows-msvc
```

### Debug 调试包

1. 在 `./src-tauri/Cargo.toml` `[dependencies] tauri = { version = "2.0.6", features = ["devtools"] }` 添加 `devtools`
2. 使用 `pnpm tauri build --debug --verbose`.

### macOS 跨架构打包 (Intel & Apple Silicon)

需要确保整个工具链都是 aarch64

```bash
$ node -p process.arch
# arm64
$ arch
# arm64
$ uname -m
# arm64

$ rustup target add x86_64-apple-darwin

$ rustup target add aarch64-apple-darwin

# 验证已安装的 targets
$ rustup target list --installed
# 应该显示：
# aarch64-apple-darwin
# x86_64-apple-darwin
$ pnpm tauri build --target universal-apple-darwin
```

### 在 macOS 打包 Windows

> https://v2.tauri.app/distribute/windows-installer/#experimental-build-windows-apps-on-linux-and-macos

```bash
$ brew install nsis
$ brew install llvm
$ cargo install --locked cargo-xwin
$ pnpm tauri build --runner cargo-xwin --target x86_64-pc-windows-msvc
```

打包出现下面的报错

```bash
warning: ring@0.17.11:   390 |   size_t index = 0;
warning: ring@0.17.11:       |          ^
warning: ring@0.17.11: 12 warnings generated.
error: failed to run custom build command for `ring v0.17.11`
```

重新安装解决 `brew install llvm@15`. https://github.com/tauri-apps/tauri/issues/9758

相关链接：

- https://github.com/rust-lang/rust/issues/63939#issuecomment-665952545

## 更新自动下载二进制包 🔥

### 1. 生成私钥

```bash
pnpm tauri signer generate -w ~/.tauri/<app-name>.key
```

### 2. 配置

#### 启用插件

在插件中启用 `tauri.config.json` Updater 插件

```js
"updater": {
  "pubkey": "你的公钥", // 公钥文件的内容
  "endpoints": ["https://example.com/updates/latest.json"]
  // "endpoints": ["https://example.com/static/{{project_name}}/updates/{{client_type}}/latest.json"]
}
```

- pubkey: 公钥位置(MacOS) `$HOME/.tauri/<app-name>.key.pub`.
- endpoints: 用于下载更新的 JSON 文件的 URL。

#### 更新 `latest.json`

> 文档：https://v2.tauri.app/plugin/updater/#static-json-file

每次发布新版本，都需要更新 `latest.json` 文件。

1. platforms<target>

- `darwin-x86_64`: 用于 Intel 芯片的 Mac
- `darwin-aarch64`: 用于 Apple Silicon (M1/M2/M3) 芯片的 Mac
- `windows-x86_64`: 用于 64 位 Windows
- `linux-x86_64`: 用于 64 位 Linux

2. url:

- 链接的文件后缀应为 `.app.tar.gz` 格式，此格式专用于更新包，它能自动解压和替换应用文件。
- 打包后输出在 `src-tauri/target/<debug|release>/bundle/macos/<app-name>.app.tar.gz (updater)`
- 需要把该文件上传到 CDN，更新 `latest.json` 中的 `url`。

3. signature:

- 签名文件打包后输出在 `src-tauri/target/<debug|release>/bundle/macos/<app-name>.app.tar.gz.sig`.
- 打开文件，复制贴到 `signature`.

```json
{
  "version": "1.0.0",
  "platforms": {
    "darwin-x86_64": {
      "url": "https://example.com/updates/v1.0.0/darwin-x86_64.app.tar.gz",
      "signature": "签名内容 BASE64_SIGNATURE_HERE"
    },
    "darwin-aarch64": {}
  }
}
```

4. 最后，上传到 `/cdn/static/<project>/updates/${client_type}/` 目录下，并根据版本号创建对应目录。

```bash
# CDN Path: /cdn/static/project1/updates/desktop/
v1.0.0
  xxxx-1.0.0-arm64.app.tar.gz
  xxxx-1.0.0-x64.app.tar.gz
  xxxx-1.0.0-x64-setup.exe
latest.json
```

#### 版本目录结构

```bash
cdn/
    static/
      project1
        updates/
          desktop/
            latest.json  // 桌面端最新版本
            v1.0.0/
              win-x64.exe       // Windows
              macos-x64.dmg     // macOS Intel
              macos-aarch64.dmg // macOS Apple Silicon
              // ...
            v1.0.1/
              win-x64.exe
              // ...
          app/
            latest.json  // App 端最新版本
            v1.0.0/
              app.apk
            v1.0.1/
              app.apk
        templates
        data
      project2
        // ...
```

### 3. 检查更新

> https://v2.tauri.app/plugin/updater/#checking-for-updates

在 JavaScript 中使用 @tauri-apps/plugin-updater 检查更新，例如：

```js
import { check } from "@tauri-apps/plugin-updater";
import { relaunch } from "@tauri-apps/plugin-process";

async function checkForUpdates() {
  const update = await check();
  if (update.shouldUpdate) {
    await update.downloadAndInstall();
    await relaunch();
  }
}
```

## 常用功能指南

### 自定义可拖动元素

```css
[data-tauri-drag-region] {
  -webkit-app-region: drag;
}
```

```tsx
<Wrapper data-tauri-drag-region>...</Wrapper>
```

### 跳转的应用也要需要可拖动

```js
{
  "permissions": [
    {
      "identifier": "core:window:allow-start-dragging",
      "allow": [
        {
          "window": "main",
          "url": ["local", "http://localhost:*", "https://*.abc.cn"]
        }
      ]
    },
  ]
}
```

### 设置托盘图标

> 会有警告：https://github.com/tauri-apps/tauri/issues/12382

```js
{
  "app":{
    "trayIcon": {
      "iconPath": "icons/icon.png",
      "menuOnLeftClick": true
    }
  }
}
```

### 生成图标

> https://v1.tauri.app/v1/guides/features/icons/

1. 准备一张 1014x1024 名字为 app-icon.png 图片，放到根目录。
2. 再执行 `pnpm tauri icon`.

## FQA

### 打包执行完最后报：`Invalid symbol 126, offset 0.`

问题与私钥路径设置有关。符号 126 对应的是波浪符号 `~`。

```bash
# 替换这个
export TAURI_SIGNING_PRIVATE_KEY="~/.tauri/app-name.key"

# 使用完整路径
export TAURI_SIGNING_PRIVATE_KEY="/home/<USER>/.tauri/app-name.key"
# 或Windows上
set TAURI_SIGNING_PRIVATE_KEY=C:\Users\<USER>\.tauri\app-name.key
```

```bash
export TAURI_SIGNING_PRIVATE_KEY="$HOME/.tauri/app-name.key"
```

### warning: use of deprecated field `tauri::tauri_utils::config::TrayIconConfig::menu_on_left_click`: Use `show_menu_on_left_click` instead.

> https://github.com/tauri-apps/tauri/issues/12382

### REF

> https://v2.tauri.app/zh-cn/plugin/updater/

> https://github.com/tauri-apps/tauri/discussions/2776
