# NextJS 项目部署实践：基于阿里云 ECS

## 服务器信息

```bash
cat /etc/os-release
NAME="Alibaba Cloud Linux"
VERSION="3 (OpenAnolis Edition)"
ID="alinux"
ID_LIKE="rhel fedora centos anolis"
VERSION_ID="3"
VARIANT="OpenAnolis Edition"
VARIANT_ID="openanolis"
ALINUX_MINOR_ID="2104"
ALINUX_UPDATE_ID="11"
PLATFORM_ID="platform:al8"
PRETTY_NAME="Alibaba Cloud Linux 3.2104 U11 (OpenAnolis Edition)"
ANSI_COLOR="0;31"
HOME_URL="https://www.aliyun.com/"
```

```bash
node -v
# v18.19.0
npm -v
# 10.2.3
pnpm -v
# 10.7.1
mysql --version
# mysql  Ver 8.0.41 for Linux on x86_64 (Source distribution)
pm2 -v
# 6.0.5
```

## 环境准备

### 创建数据库

```bash
# 查看版本号
mysql --version

# 重新启动正常的 MySQL 服务
sudo systemctl start mysqld

# 检查服务状态
sudo systemctl status mysqld

# 查找初始临时密码
sudo grep 'temporary password' /var/log/mysqld.log

# 登录设置密码
mysql -u root -p

# 设置密码
ALTER USER 'root'@'localhost' IDENTIFIED WITH mysql_native_password BY '你的新密码';

FLUSH PRIVILEGES;

# 登录
mysql -u root -p

# 输入密码后，出现了 mysql> 提示符

# 说明你已经成功登录了 MySQL。

# 验证

# -- 查看当前数据库
SELECT DATABASE();

# -- 查看所有数据库
SHOW DATABASES;

# -- 创建数据库
CREATE DATABASE your_database_name;

# -- 切换到你的数据库
USE your_database_name;

# -- 查看有哪些表
SHOW TABLES;

# -- 看看某张表结构（比如 users 表）
DESCRIBE users;
```

### 环境变量配置

```bash
# 创建 .env 文件
touch .env

# 添加必要的环境变量
DATABASE_URL="mysql://root:your_password@localhost:3306/your_database_name"
NEXTAUTH_URL="https://your_domain"
NEXTAUTH_SECRET="your_secret" # 可以用 openssl rand -base64 32 生成
```

## 项目部署

### 安装依赖并构建

```bash
# 安装依赖
pnpm install

# 生成 Prisma 客户端
pnpm prisma generate

# 同步数据库结构
pnpm prisma db push

# 构建项目
pnpm build
```

## 启动方式

### 1. pnpm 方式

```bash
# 直接启动
pnpm start
```

### 2. PM2 方式（推荐）

```bash
pnpm add -g pm2

pm2 -v
# 6.0.6
```

遇到 pm2 状态异常、进程卡住、cluster 不一致，优先 `pm2 update`。

ecosystem.config.js 配置：

```js
module.exports = {
  apps: [
    {
      name: require("./package.json").name,
      script: "node_modules/next/dist/bin/next",
      args: "start",
      instances: "max", // 设置为 1 则只启动一个实例
      exec_mode: "cluster",
      watch: false,
      env: {
        PORT: 8000, // 确保端口号与实际使用的一致
        NODE_ENV: "production",
      },
    },
  ],
};
```

```bash
# 启动服务
pm2 start ecosystem.config.js

# 保存 PM2 进程列表
pm2 save

# 设置开机自启
pm2 startup

# 查看运行状态
pm2 status

# 查看日志
pm2 logs

# 监控
pm2 monit
```

## 外网访问配置

### 检查防火墙状态

```bash
systemctl status firewalld
```

阿里云服务器默认关闭防火墙（inactive），主要通过安全组控制访问。

### 配置安全组

1. 登录阿里云控制台
2. 找到 ECS 实例
3. 点击"安全组" -> "配置规则"
4. 添加入方向规则：
   - 协议：TCP
   - 端口范围：8000（或你的应用端口）
   - 授权对象：0.0.0.0/0
   - 优先级：1

### 验证部署

```bash
# 检查端口监听状态
netstat -tlnp | grep 8000

# 查看公网IP
curl cip.cc

# 访问测试
http://你的公网IP:8000
```

## 注意事项

1. PM2 多实例说明：

   - `instances: "max"` 会根据 CPU 核心数启动对应数量的实例
   - 可以通过 `nproc` 命令查看 CPU 核心数
   - 多实例有助于负载均衡和高可用
   - 如果内存受限，可以将 instances 设置为 1

2. 端口选择：

   - 确保 ecosystem.config.js 中的端口与实际使用的端口一致
   - 记得在安全组中开放对应端口

3. 日志管理：

   - PM2 自动管理日志
   - 可以通过 `pm2 logs` 查看实时日志
   - 建议配置日志轮转避免占用过多磁盘空间

4. 性能优化：

   - 监控内存使用：`pm2 monit`
   - 需要时可以在 PM2 配置中设置内存限制：`node_args: '--max-old-space-size=4096'`

5. 安全建议：
   - 建议使用域名 + HTTPS
   - 定期检查日志排查问题
   - 设置强密码
   - 考虑限制特定 IP 访问
