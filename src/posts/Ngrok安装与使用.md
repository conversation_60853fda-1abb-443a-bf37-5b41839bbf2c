# Ngrok 安装与使用

## 安装 ngrok（Homebrew）

如已有旧版本（如 yarn 全局装的），先清理：

```bash
rm /usr/local/bin/ngrok
```

安装：

```bash
brew install --cask ngrok
```

成功后会输出：

```
🍺  ngrok was successfully installed!
```

## 添加 authtoken（绑定账号）

```bash
ngrok config add-authtoken <你的-token>
```

token 会写入 `~/.ngrok/ngrok.yml`。

## 快速启动一个端口（临时）

```bash
ngrok http 3000
```

输出中会显示公网地址，例如：

```
Forwarding  https://abcd1234.ngrok.io -> http://localhost:3000
```

## 使用配置文件（多隧道）

编辑 `~/.ngrok/ngrok.yml`：

```yaml
authtoken: <你的-token>
tunnels:
  web:
    addr: 3000
    proto: http
  api:
    addr: 5000
    proto: http
```

运行：

```bash
ngrok start web       # 启动指定隧道
ngrok start --all     # 启动所有隧道
```

## 常用命令速查

```bash
ngrok http 3000         # 本地服务临时公网访问
ngrok tcp 22            # 暴露 SSH（慎用）
ngrok start --all       # 启动配置文件中全部隧道
```
