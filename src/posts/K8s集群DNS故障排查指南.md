# K8s 集群 DNS 故障排查指南

## 常见现象

- Pod 间歇性无法解析外部域名，导致 Redis/数据库等服务连接失败
- CoreDNS/kube-dns 日志报 `i/o timeout`、`SERVFAIL`、`read udp ... timeout` 等
- 业务 Pod 日志报 `getaddrinfo ENOTFOUND`、`ETIMEDOUT` 等

---

## 典型报错日志

### CoreDNS/kube-dns 日志

```
[ERROR] plugin/errors: 2 853314384103825015.3552245806284899840. HINFO: read udp 10.233.92.2:47415->183.60.82.98:53: i/o timeout
[ERROR] plugin/errors: 2 4786562091835325386.8529863116054925007.in-addr.arpa. HINFO: read udp 10.233.92.2:50118->183.60.83.19:53: i/o timeout
```

### 业务 Pod 日志

```
nodejs.ETIMEDOUTError: connect ETIMEDOUT
at Socket.<anonymous> (/usr/src/app/node_modules/ioredis/built/redis/index.js:327:37)
...
errorno: "ETIMEDOUT"
code: "ETIMEDOUT"
syscall: "connect"
```

---

## 常用排查命令

### 1. 检查 DNS Pod 状态

```bash
kubectl get pods -n kube-system -l k8s-app=coredns -o wide
kubectl get pods -n kube-system -l k8s-app=kube-dns -o wide
```

### 2. 在业务 Pod 内测试 DNS 解析

```bash
kubectl exec -it <pod> -n <ns> -- nslookup www.baidu.com
kubectl exec -it <pod> -n <ns> -- dig www.baidu.com
```

### 3. 检查 CoreDNS 日志

```bash
kubectl logs -n kube-system -l k8s-app=coredns
kubectl logs -n kube-system -l k8s-app=kube-dns
```

### 4. 检查节点 DNS 配置

```bash
cat /etc/resolv.conf
resolvectl status
```

### 5. 测试节点到外部 DNS 的连通性

```bash
dig @114.114.114.114 www.baidu.com
dig @8.8.8.8 www.baidu.com
nslookup www.baidu.com 114.114.114.114
```

### 6. 查看 systemd-resolved 配置和状态（Ubuntu）

```bash
systemctl status systemd-resolved
resolvectl status
cat /etc/systemd/resolved.conf
resolvectl dns
```

---

## 常见原因

- CoreDNS 配置 `forward . /etc/resolv.conf`，而宿主机 `/etc/resolv.conf` 指向 127.0.0.53（systemd-resolved），实际用的上游 DNS 不稳定
- 上游 DNS（如 183.60.83.19/183.60.82.98）丢包严重或不支持 EDNS0
- 节点防火墙/安全组/网络策略拦截了 53 端口
- CoreDNS Pod 所在节点网络插件（如 calico-node）异常
- CoreDNS/kube-dns Pod 资源不足被 OOMKilled

## 修复建议

- **优先推荐**：直接在 CoreDNS ConfigMap 里写死多个可靠的上游 DNS（如 114.114.114.114、8.8.8.8、223.5.5.5），避免依赖宿主机的 systemd-resolved
  ```yaml
  forward . 114.114.114.114 8.8.8.8 223.5.5.5 { prefer_udp max_concurrent 1000 }
  ```
- 如果必须用公司/云厂商内网 DNS，也要写在 forward 里，优先写内网 DNS
- 改完后 `kubectl -n kube-system rollout restart deployment coredns`
- 节点上 `/etc/systemd/resolved.conf` 也可以配置 DNS，重启 systemd-resolved 后生效，但不如直接写死 CoreDNS 上游稳定
- 检查防火墙/安全组，确保 53 端口 UDP/TCP 放行
- CoreDNS/kube-dns Pod 建议加资源 requests/limits，避免 OOM

## 最佳实践

- CoreDNS 上游 DNS 推荐写多个，防止单点故障
- 优先用公司/云厂商内网 DNS，其次补充公共 DNS
- 定期监控 CoreDNS/kube-dns 状态和日志，及时发现异常
- 业务 Pod 遇到 DNS 问题，优先排查 CoreDNS/kube-dns、节点网络和上游 DNS 连通性

---

参考：[给 Kubernetes 集群新增外部 DNS 服务](https://www.chenshaowen.com/blog/add-outer-dns-server-to-kubernetes-cluster.html)
