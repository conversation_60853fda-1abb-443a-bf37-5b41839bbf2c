# Prompt 工程学习记录

本记录总结了 Claude Prompt 工程化的核心知识点，适合有开发经验的同学查阅。每个关键点都配有简明示例，便于工程落地。

---

## 1. 结构化输出（Structured Output）

**要点**：让模型输出 JSON/Markdown/表格/代码块，便于自动解析和 UI 渲染。

**示例**：

```
请用如下 JSON 格式输出：
{
  "title": "",
  "summary": "",
  "tags": []
}
```

---

## 2. Chain-of-Thought（CoT，分步推理）

**要点**：让模型分步推理，显式暴露中间思考过程，提升可解释性。

**示例**：

```
请分步推理，详细写出每一步，最后单独输出答案。
Step 1: ...
Step 2: ...
Answer: ...
```

---

## 3. Few-shot Prompting（示例驱动）

**要点**：用输入-输出示例提升模型泛化和可控性。

**示例**：

```
输入：苹果
输出：水果

输入：西兰花
输出：蔬菜

输入：牛奶
输出：
```

---

## 4. 幻觉规避（Hallucination Avoidance）

**要点**：用“不知道就说不知道”“限定信息来源”等 prompt 设计，降低模型胡编风险。

**示例**：

```
如果你不确定答案，请直接回复“我不知道”，不要编造内容。
```

---

## 5. 复杂场景 Prompt 设计

**要点**：分层（system/role/示例/指令）、模块化、结构化，适合多轮对话、行业知识注入。

**示例**：

```
System: 你是资深金融分析师，所有回答都要有数据依据。
Role: 你现在扮演一名银行信贷经理。
示例：
输入：客户A的年收入50万，负债20万，能否贷款100万？
输出：根据客户A的收入和负债情况……
本轮问题：客户B的年收入30万，负债5万，能否贷款50万？
```

---

## 6. Prompt Chaining（链式提示）

**要点**：多步流程拆解，每步单独 prompt，串联成 pipeline。

**示例**：

```
【Step 1】
请从下列文本中抽取所有公司名称，输出 JSON 数组。
文本：{{原始文本}}

【Step 2】
请根据上一步输出的公司名称，查询每家公司的行业分类，输出如下 JSON：
[
  {"公司": "xxx", "行业": "yyy"},
  ...
]
```

---

## 7. Tool Use（工具调用）

**要点**：模型主动调用外部 API/函数/服务，实现“模型+工具”混合智能。

**示例**：

```
你可以调用以下工具：
- calculator(expression): 计算表达式
- search(query): 检索互联网信息

如果需要调用工具，请用如下格式输出：
[TOOL: 工具名(参数)]
```

---

## 8. Search & Retrieval（RAG，检索增强）

**要点**：先检索资料，再让模型只基于检索内容生成答案，提升事实性和可控性。

**示例**：

```
请只根据下列资料回答问题，不允许引入其他信息：
【资料1】...
【资料2】...
问题：xxx
```

---

### 工程化技术细节

- **检索系统选型**：

  - 推荐向量数据库（如 Milvus、Pinecone、Weaviate）、ElasticSearch、企业自建 API。
  - 检索方式可选向量召回（embedding）、关键词召回、混合检索。

- **数据格式与分段**：

  - 检索结果建议分段编号（如【资料 1】【资料 2】），便于引用和溯源。
  - 支持 Markdown、JSON、纯文本等格式，按业务需求选择。

- **Prompt 拼接**：

  - 检索结果拼接到 prompt 前部，问题置于最后。
  - 明确要求“只能基于资料回答”，防止模型幻觉。

- **置信度与引用**：

  - 可要求模型输出引用/出处字段，或置信度分数，便于后端自动化校验。
  - 示例：
    ```json
    {
      "answer": "...",
      "source": "资料2",
      "confidence": 0.92
    }
    ```

- **RAG 流程建议**：

  1. 用户输入 → 2. 检索相关资料 → 3. 拼接 prompt → 4. 大模型生成 → 5. 解析引用/置信度 → 6. 输出/兜底

- **常见坑与优化**：
  - 检索结果过多时需截断，优先保留高相关度片段，防止 prompt 超长。
  - 检索内容要去重、去噪，提升相关性。
  - 业务关键场景建议后端二次校验模型输出，必要时人工兜底。
  - 日志和引用建议持久化，便于追溯和优化。

---

> 建议：所有工程化场景优先结构化输出，复杂任务用链式 prompt 拆解，智能体开发必用 Tool Use + Chaining + RAG 组合拳。

## 9. 复杂场景 Prompt 设计

**要点**：分层（system/role/示例/指令）、模块化、结构化，适合多轮对话、行业知识注入。

**示例**：

```
System: 你是资深金融分析师，所有回答都要有数据依据。
Role: 你现在扮演一名银行信贷经理。
示例：
输入：客户A的年收入50万，负债20万，能否贷款100万？
输出：根据客户A的收入和负债情况……
本轮问题：客户B的年收入30万，负债5万，能否贷款50万？
```

---

### 工程化技术细节与实战建议

- **分层与模块化**：

  - System prompt 统一全局规则，Role prompt 注入角色/行业知识，Few-shot 示例提升泛化，指令区聚焦本轮任务。
  - 推荐用模板/配置管理各层内容，便于动态拼接和维护。

- **上下文管理**：

  - 多轮对话建议用消息数组管理历史，防止丢失关键信息。
  - 复杂业务流可用“上下文快照”或“状态机”管理。

- **行业知识注入**：

  - 通过 system/role prompt 或示例注入行业规则、术语、流程。
  - 适合金融、法律、医疗等高门槛场景。

- **结构化输出**：

  - 复杂场景建议输出多字段、嵌套 JSON，便于自动化处理。

- **业务规则约束**：

  - Prompt 明确写出必须遵守的规则/流程/边界条件。

- **实战建议**：
  - 复杂场景优先用“分层+模块化”Prompt，便于维护和复用。
  - 行业场景建议用“知识注入+规则约束”双保险。
  - 输出结构优先用 JSON/Markdown，便于自动化解析。
  - 复杂业务流建议结合 Prompt Chaining 拆解多步。

---

## 10. Prompt Chaining / Tool Use / RAG 组合拳

**要点**：多步流程拆解（Chaining）、模型+工具混合智能（Tool Use）、检索增强（RAG），三者组合打造高可控、高可靠的智能体系统。

**示例**：

```
【Step 1】
请从下列文本中抽取所有公司名称，输出 JSON 数组。
文本：{{原始文本}}

【Step 2】
请根据上一步输出的公司名称，查询每家公司的行业分类，输出如下 JSON：
[
  {"公司": "xxx", "行业": "yyy"},
  ...
]

【Step 3】
如需查找公司信息，可调用 search(query) 工具。
[TOOL: search("xxx 公司信息")]
```

---

### 工程化技术细节与实战建议

- **Prompt Chaining Orchestrator 设计**：

  - 用 Node.js/Python/TS 等实现 orchestrator，自动拼接 prompt、解析输出、串联多步。
  - 每步输出结构化，便于自动流转和异常处理。
  - 支持线性链、分支链、反射链等多种链式模式。

- **错误处理与回滚**：

  - 每步都做异常检测，失败可重试或回滚，适合生产级应用。

- **多模型/多工具协作**：

  - 某些步骤可用不同模型/外部工具（如检索、计算、API），适合插件化、RPA、智能体分工。

- **Tool Use 调用协议**：

  - 统一调用格式（如 [TOOL: ...]、JSON、特殊标记），便于自动解析和执行。
  - 工具列表和参数建议用配置/注册表管理，便于扩展和维护。

- **RAG+Tool Use+Chaining 组合拳**：

  - 检索增强（RAG）用于事实查找，Tool Use 负责外部能力调用，Prompt Chaining 串联多步流程。
  - 复杂场景建议三者结合，打造“会思考+会用工具+有知识”的混合智能体。

- **日志与可追溯性**：

  - 工具调用、检索结果、每步输出建议持久化，便于追溯和优化。

- **实战建议**：
  - 复杂业务建议用配置/DSL 管理链路，便于维护和扩展。
  - 业务关键场景建议后端二次校验模型输出，必要时人工兜底。

---
